import type { FC } from 'react';
import { useState, useEffect } from 'react';
import { Modal, Form, Input, Space, Typography, Divider, message } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import { addOrUpdateStoreIcon } from '@/services/api/store';
import ProSvg from '@/common/components/ProSvg';
import useImageUpload from './hooks/useImageUpload';

// 添加样式
const modalStyles = `
  .image-upload-container:hover .hover-mask {
    opacity: 1 !important;
  }
`;

// 创建样式标签
if (typeof document !== 'undefined') {
  const styleElement = document.createElement('style');
  styleElement.textContent = modalStyles;
  if (!document.head.querySelector('style[data-component="CreateIconModal"]')) {
    styleElement.setAttribute('data-component', 'CreateIconModal');
    document.head.appendChild(styleElement);
  }
}

type IconInfo = {
  id?: string | number;
  name: string;
  icon: string; // 未选中
  icon_active: string; // 选中
};

type CreateIconModalProps = {
  open: boolean;
  onCancel: () => void;
  onOk?: (icon: IconInfo) => void;
  storeId?: string | number;
  editing?: IconInfo;
};

const CreateIconModal: FC<CreateIconModalProps> = ({ open, onCancel, onOk, storeId, editing }) => {
  const [form] = Form.useForm();
  const [activeImageUrl, setActiveImageUrl] = useState<string>('');
  const [inactiveImageUrl, setInactiveImageUrl] = useState<string>('');

  // 图片上传处理函数
  const handleImageUpload = (type: 'active' | 'inactive', imageUrl: string) => {
    if (type === 'active') {
      setActiveImageUrl(imageUrl);
    } else {
      setInactiveImageUrl(imageUrl);
    }
  };

  // 使用图片上传hook
  const { handleUpload, isUploading } = useImageUpload<'active' | 'inactive'>(handleImageUpload);

  // 图标渲染函数 - 与RudderNavForm.tsx保持一致
  const renderIcon = (src?: string) => {
    if (!src)
      return (
        <div
          style={{
            width: '64px',
            height: '64px',
            backgroundColor: '#f3f3f3',
            border: '1px dashed #ddd',
            borderRadius: '6px',
          }}
        />
      );
    return typeof src === 'string' && src.includes('http') ? (
      <img
        src={src}
        style={{
          maxWidth: '100%',
          maxHeight: '100%',
          width: 'auto',
          height: 'auto',
          objectFit: 'contain',
        }}
      />
    ) : (
      <ProSvg src={src} width="100%" height="100%" />
    );
  };

  // 当编辑的图标变化时，重置状态和表单
  useEffect(() => {
    if (open) {
      // 重置图片上传状态
      setActiveImageUrl(editing?.icon_active || '');
      setInactiveImageUrl(editing?.icon || '');

      // 重置并设置表单初始值
      form.resetFields();
      if (editing?.name) {
        form.setFieldsValue({
          name: editing.name,
          activeIcon: editing.icon_active,
          inactiveIcon: editing.icon,
        });
      }
    }
  }, [open, editing, form]);

  // 当图片上传状态变化时，更新表单字段
  useEffect(() => {
    form.setFieldsValue({
      activeIcon: activeImageUrl || editing?.icon_active,
      inactiveIcon: inactiveImageUrl || editing?.icon,
    });
  }, [activeImageUrl, inactiveImageUrl, editing, form]);

  const handleOk = async () => {
    try {
      // 验证表单字段（包括自定义校验）
      await form.validateFields();

      // 获取表单值
      const values = form.getFieldsValue();

      const payload: IconInfo = {
        id: editing?.id,
        name: values.name,
        icon_active: activeImageUrl || editing?.icon_active || '',
        icon: inactiveImageUrl || editing?.icon || '',
      };

      await addOrUpdateStoreIcon({
        id: payload.id,
        storeId: storeId as any,
        name: payload.name,
        selectedIcon: payload.icon_active,
        unselectedIcon: payload.icon,
      });

      message.success(editing?.id ? '编辑成功' : '新增成功');
      onOk?.({ ...payload, id: payload.id });
    } catch (err) {
      // 如果是表单验证错误，不需要额外处理，antd会自动显示错误信息
    }
  };

  // 编辑态初始化见上方 initialActive/initialInactive

  return (
    <Modal
      width={720}
      title="新建图标"
      open={open}
      onCancel={onCancel}
      onOk={handleOk}
      okText="保存"
      cancelText="取消"
      destroyOnClose
    >
      <Form form={form} layout="vertical" preserve={false}>
        <Form.Item
          label="图标名称："
          name="name"
          rules={[{ required: true, message: '请输入图标名称' }]}
        >
          <Input placeholder="请输入" maxLength={8} showCount />
        </Form.Item>

        <div style={{ marginBottom: 8, fontWeight: 500 }}>上传图标：</div>
        <Typography.Text type="secondary">支持jpg/jpeg/png格式，建议尺寸100*100px</Typography.Text>
        <div style={{ margin: '12px 0 16px' }} />
        {/* <Divider style={{ margin: '12px 0 16px' }} /> */}

        <Space size={40} align="start">
          {/* 选中状态图标上传 */}
          <div style={{ textAlign: 'center' }}>
            <div>
              {!(activeImageUrl || editing?.icon_active) ? (
                <div
                  style={{ display: 'inline-flex', flexDirection: 'column', alignItems: 'center' }}
                >
                  <div
                    style={{
                      width: '120px',
                      height: '120px',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      flexDirection: 'column',
                      background: '#fafafa',
                      border: '1px dashed #d9d9d9',
                      borderRadius: '8px',
                      cursor: isUploading('active') ? 'not-allowed' : 'pointer',
                      opacity: isUploading('active') ? 0.6 : 1,
                    }}
                    onClick={() => !isUploading('active') && handleUpload('active')}
                  >
                    <PlusOutlined
                      style={{ fontSize: '24px', color: '#bfbfbf', marginBottom: '8px' }}
                    />
                    <div style={{ color: '#bfbfbf', fontSize: '12px' }}>
                      {isUploading('active') ? '上传中...' : ''}
                    </div>
                  </div>
                </div>
              ) : (
                <div
                  style={{ display: 'inline-flex', flexDirection: 'column', alignItems: 'stretch' }}
                >
                  <div
                    className="image-upload-container"
                    style={{
                      position: 'relative',
                      width: '120px',
                      height: '120px',
                      background: '#f7f7f7',
                      borderRadius: '8px',
                      cursor: isUploading('active') ? 'not-allowed' : 'pointer',
                      opacity: isUploading('active') ? 0.6 : 1,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}
                    onClick={() => !isUploading('active') && handleUpload('active')}
                  >
                    <div
                      style={{
                        width: '64px',
                        height: '64px',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}
                    >
                      {renderIcon(activeImageUrl || editing?.icon_active)}
                    </div>
                    <div
                      style={{
                        position: 'absolute',
                        left: 0,
                        right: 0,
                        bottom: 0,
                        height: '40px',
                        background: 'rgba(0, 0, 0, 0.45)',
                        color: '#fff',
                        textAlign: 'center',
                        lineHeight: '40px',
                        borderRadius: '0 0 8px 8px',
                        opacity: 0,
                        transition: 'opacity 0.2s ease',
                        cursor: 'pointer',
                      }}
                      className="hover-mask"
                    >
                      {isUploading('active') ? '上传中...' : '更换图片'}
                    </div>
                  </div>
                </div>
              )}
            </div>
            <div style={{ marginTop: 8, color: '#999' }}>选中状态</div>
            <Form.Item
              name="activeIcon"
              style={{ marginTop: -30, textAlign: 'left' }}
              rules={[{ required: true, message: '请上传选中状态图标' }]}
            >
              <div style={{ display: 'none' }} />
            </Form.Item>
          </div>

          {/* 未选中状态图标上传 */}
          <div style={{ textAlign: 'center' }}>
            <div>
              {!(inactiveImageUrl || editing?.icon) ? (
                <div
                  style={{ display: 'inline-flex', flexDirection: 'column', alignItems: 'center' }}
                >
                  <div
                    style={{
                      width: '120px',
                      height: '120px',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      flexDirection: 'column',
                      background: '#fafafa',
                      border: '1px dashed #d9d9d9',
                      borderRadius: '8px',
                      cursor: isUploading('inactive') ? 'not-allowed' : 'pointer',
                      opacity: isUploading('inactive') ? 0.6 : 1,
                    }}
                    onClick={() => !isUploading('inactive') && handleUpload('inactive')}
                  >
                    <PlusOutlined
                      style={{ fontSize: '24px', color: '#bfbfbf', marginBottom: '8px' }}
                    />
                    <div style={{ color: '#bfbfbf', fontSize: '12px' }}>
                      {isUploading('inactive') ? '上传中...' : ''}
                    </div>
                  </div>
                </div>
              ) : (
                <div
                  style={{ display: 'inline-flex', flexDirection: 'column', alignItems: 'stretch' }}
                >
                  <div
                    className="image-upload-container"
                    style={{
                      position: 'relative',
                      width: '120px',
                      height: '120px',
                      background: '#f7f7f7',
                      borderRadius: '8px',
                      cursor: isUploading('inactive') ? 'not-allowed' : 'pointer',
                      opacity: isUploading('inactive') ? 0.6 : 1,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}
                    onClick={() => !isUploading('inactive') && handleUpload('inactive')}
                  >
                    <div
                      style={{
                        width: '64px',
                        height: '64px',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}
                    >
                      {renderIcon(inactiveImageUrl || editing?.icon)}
                    </div>
                    <div
                      style={{
                        position: 'absolute',
                        left: 0,
                        right: 0,
                        bottom: 0,
                        height: '40px',
                        background: 'rgba(0, 0, 0, 0.45)',
                        color: '#fff',
                        textAlign: 'center',
                        lineHeight: '40px',
                        borderRadius: '0 0 8px 8px',
                        opacity: 0,
                        transition: 'opacity 0.2s ease',
                        cursor: 'pointer',
                      }}
                      className="hover-mask"
                    >
                      {isUploading('inactive') ? '上传中...' : '更换图片'}
                    </div>
                  </div>
                </div>
              )}
            </div>
            <div style={{ marginTop: 8, color: '#999' }}>未选中状态</div>
            <Form.Item
              name="inactiveIcon"
              style={{ marginTop: -30, textAlign: 'left' }}
              rules={[{ required: true, message: '请上传未选中状态图标' }]}
            >
              <div style={{ display: 'none' }} />
            </Form.Item>
          </div>
        </Space>
      </Form>
    </Modal>
  );
};

export default CreateIconModal;


